import React, { use<PERSON><PERSON>back, useEffect, useState, ChangeEvent } from "react";
import Switch from "react-switch";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { MkdSimpleTable } from "../../../components/MkdSimpleTable";
import { PlusIcon, UserIcon } from "../../../assets/svgs";
import styles from "./AdminRewardsAndReferralsListPage.module.css";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { useContexts } from "../../../hooks/useContexts";
import { ToastStatusEnum } from "../../../utils/Enums";
import emptyPageImg from "@/assets/images/empty-page.png";

interface RewardActivity {
  id: number;
  user: string;
  type: string;
  amount: string;
  date: string;
}

interface RewardSettings {
  referral_bonus_amount: number;
  loyalty_points_rate: number;
  referral_program_active: boolean;
  loyalty_program_active: boolean;
}

interface RewardSummary {
  total_rewards_last_30_days: number;
  active_referrers_this_month: number;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const AdminRewardsAndReferralsListPage = () => {
  const { sdk } = useSDK();
  const { showToast } = useContexts();
  const [loadingSummary, setLoadingSummary] = useState(true);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [savingSettings, setSavingSettings] = useState(false);
  const [summary, setSummary] = useState<RewardSummary | null>(null);
  const [settings, setSettings] = useState<RewardSettings | null>(null);
  const [inputValues, setInputValues] = useState({
    referral_bonus_amount: "",
    loyalty_points_rate: "",
  });
  const [activities, setActivities] = useState<RewardActivity[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [apiParams, setApiParams] = useState({ page: 1, limit: 10 });

  const fetchSummary = useCallback(async () => {
    setLoadingSummary(true);
    try {
      const res = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/rewards/summary",
        method: "GET",
      });
      if (!res.error) {
        setSummary(res.data);
      }
    } finally {
      setLoadingSummary(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchSettings = useCallback(async () => {
    console.log("Fetching settings...");
    setLoadingSettings(true);
    try {
      const res = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/rewards/settings",
        method: "GET",
      });
      console.log("Settings API response:", res);

      if (!res.error) {
        console.log("Settings loaded successfully:", res.data);
        setSettings(res.data);
        // Update input values when settings are loaded
        setInputValues({
          referral_bonus_amount:
            res.data.referral_bonus_amount?.toString() || "",
          loyalty_points_rate: res.data.loyalty_points_rate?.toString() || "",
        });
      } else {
        console.error("Settings API error:", res.error);
        // Set default settings if API fails
        const defaultSettings = {
          referral_bonus_amount: 0,
          loyalty_points_rate: 0,
          referral_program_active: false,
          loyalty_program_active: false,
        };
        setSettings(defaultSettings);
        setInputValues({
          referral_bonus_amount: "0",
          loyalty_points_rate: "0",
        });
      }
    } catch (error) {
      console.error("Exception during settings fetch:", error);
      // Set default settings if exception occurs
      const defaultSettings = {
        referral_bonus_amount: 0,
        loyalty_points_rate: 0,
        referral_program_active: false,
        loyalty_program_active: false,
      };
      setSettings(defaultSettings);
      setInputValues({
        referral_bonus_amount: "0",
        loyalty_points_rate: "0",
      });
    } finally {
      setLoadingSettings(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchActivities = useCallback(async () => {
    setLoadingActivities(true);
    try {
      const res = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/rewards/activities",
        method: "GET",
        params: apiParams,
      });
      if (!res.error) {
        setActivities(res.data);
        setPagination((res as any).pagination);
      }
    } finally {
      setLoadingActivities(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiParams]);

  useEffect(() => {
    fetchSummary();
    fetchSettings();
  }, [fetchSummary, fetchSettings]);

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  const handleSettingsChange = (
    key: keyof RewardSettings,
    value: string | boolean
  ) => {
    console.log("handleSettingsChange called:", { key, value, settings });

    if (!settings) {
      console.log("No settings object, returning early");
      return;
    }

    if (key === "referral_bonus_amount" || key === "loyalty_points_rate") {
      const newSettings = {
        ...settings,
        [key]: parseFloat(value as string) || 0,
      };
      console.log("Updating numeric setting:", newSettings);
      setSettings(newSettings);
    } else {
      const newSettings = {
        ...settings,
        [key]: value as boolean,
      };
      console.log("Updating boolean setting:", newSettings);
      setSettings(newSettings);
    }
  };

  const handleSaveChanges = async () => {
    console.log("Save button clicked");
    console.log("Current settings:", settings);
    console.log("Current input values:", inputValues);

    if (!settings) {
      console.log("No settings object, creating from input values");
      // Create settings object from input values
      const newSettings = {
        referral_bonus_amount:
          parseFloat(inputValues.referral_bonus_amount) || 0,
        loyalty_points_rate: parseFloat(inputValues.loyalty_points_rate) || 0,
        referral_program_active: false,
        loyalty_program_active: false,
      };
      setSettings(newSettings);

      setSavingSettings(true);
      try {
        console.log("Sending request to save new settings...", newSettings);
        const res = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/admin/rewards/settings",
          method: "PUT",
          body: newSettings,
        });

        console.log("API response:", res);

        if (!res.error) {
          showToast(
            "Settings saved successfully!",
            5000,
            ToastStatusEnum.SUCCESS
          );
        } else {
          console.error("API returned error:", res.error);
          showToast(
            res.message || "Failed to save settings",
            5000,
            ToastStatusEnum.ERROR
          );
        }
      } catch (error) {
        console.error("Exception during save:", error);
        showToast("Failed to save settings", 5000, ToastStatusEnum.ERROR);
      } finally {
        setSavingSettings(false);
      }
      return;
    }

    setSavingSettings(true);
    try {
      console.log("Sending request to save settings...");
      const res = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/rewards/settings",
        method: "PUT",
        body: settings,
      });

      console.log("API response:", res);

      if (!res.error) {
        showToast(
          "Settings saved successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
      } else {
        console.error("API returned error:", res.error);
        showToast(
          res.message || "Failed to save settings",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error) {
      console.error("Exception during save:", error);
      showToast("Failed to save settings", 5000, ToastStatusEnum.ERROR);
    } finally {
      setSavingSettings(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  const columns = [
    { header: "User", accessor: "user" },
    {
      header: "Type",
      accessor: "type",
      Cell: ({ value }: { value: string }) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-semibold ${
            value.toLowerCase().includes("referral")
              ? "bg-blue-100 text-blue-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {value}
        </span>
      ),
    },
    { header: "Amount", accessor: "amount" },
    { header: "Date", accessor: "date" },
  ];

  return (
    <AdminWrapper>
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Rewards & Referrals
            </h1>
            <p className="text-gray-500">Manage platform loyalty programs</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm flex justify-between items-center">
            {loadingSummary ? (
              <Skeleton className="h-12 w-full" />
            ) : (
              <>
                <div>
                  <p className="text-gray-500 text-sm">
                    Total EBA$ Rewarded (Last 30 days)
                  </p>
                  <p className="text-2xl font-bold text-[#0F2C59]">
                    {summary?.total_rewards_last_30_days.toLocaleString() ??
                      "0"}
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <PlusIcon className="w-6 h-6 text-green-500" />
                </div>
              </>
            )}
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm flex justify-between items-center">
            {loadingSummary ? (
              <Skeleton className="h-12 w-full" />
            ) : (
              <>
                <div>
                  <p className="text-gray-500 text-sm">
                    Active Referrers (This month)
                  </p>
                  <p className="text-2xl font-bold text-[#0F2C59]">
                    {summary?.active_referrers_this_month.toLocaleString() ??
                      "0"}
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <UserIcon className="w-6 h-6 text-blue-500" />
                </div>
              </>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <h2 className="text-xl font-bold mb-4 text-[#0F2C59]">
            Reward System Configuration
          </h2>
          {loadingSettings ? (
            <Skeleton className="h-32 w-full" />
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Referral Commission
                  </label>
                  <input
                    type="text"
                    name="referral_bonus_amount"
                    value={inputValues.referral_bonus_amount}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => {
                      console.log(
                        "Referral commission input changed:",
                        e.target.value
                      );
                      setInputValues((prev) => ({
                        ...prev,
                        referral_bonus_amount: e.target.value,
                      }));
                      handleSettingsChange(
                        "referral_bonus_amount",
                        e.target.value
                      );
                    }}
                    placeholder="Enter referral commission amount"
                    className="focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border bg-white p-[.625rem] px-3 py-2 leading-tight text-gray-900 shadow focus:outline-none focus:ring-0 focus:border-blue-500 transition-colors duration-200 border-gray-300 hover:border-gray-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Loyalty Points Rate
                  </label>
                  <input
                    type="text"
                    name="loyalty_points_rate"
                    value={inputValues.loyalty_points_rate}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => {
                      console.log(
                        "Loyalty points rate input changed:",
                        e.target.value
                      );
                      setInputValues((prev) => ({
                        ...prev,
                        loyalty_points_rate: e.target.value,
                      }));
                      handleSettingsChange(
                        "loyalty_points_rate",
                        e.target.value
                      );
                    }}
                    placeholder="Enter loyalty points rate"
                    className="focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border bg-white p-[.625rem] px-3 py-2 leading-tight text-gray-900 shadow focus:outline-none focus:ring-0 focus:border-blue-500 transition-colors duration-200 border-gray-300 hover:border-gray-400"
                  />
                </div>
              </div>

              <h2 className="text-xl font-bold mt-6 mb-4 text-[#0F2C59]">
                System Controls
              </h2>
              <div className="flex items-center justify-between mb-4">
                <p className="text-gray-700">Referral Program Active</p>
                <Switch
                  onChange={(checked: boolean) =>
                    handleSettingsChange("referral_program_active", checked)
                  }
                  checked={settings?.referral_program_active || false}
                  onColor="#e63946"
                  uncheckedIcon={false}
                  checkedIcon={false}
                  handleDiameter={24}
                  height={28}
                  width={48}
                />
              </div>
              <div className="flex items-center justify-between">
                <p className="text-gray-700">Loyalty Program Active</p>
                <Switch
                  onChange={(checked: boolean) =>
                    handleSettingsChange("loyalty_program_active", checked)
                  }
                  checked={settings?.loyalty_program_active || false}
                  onColor="#e63946"
                  uncheckedIcon={false}
                  checkedIcon={false}
                  handleDiameter={24}
                  height={28}
                  width={48}
                />
              </div>
              <InteractiveButton
                onClick={handleSaveChanges}
                disabled={savingSettings}
                className="mt-6 !bg-[#0F2C59] text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {savingSettings ? "Saving..." : "Save Changes"}
              </InteractiveButton>
            </>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-bold mb-4 text-[#0F2C59]">
            Recent Reward Activities
          </h2>
          {loadingActivities ? (
            Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} className="h-8 w-full mb-2" />
            ))
          ) : activities?.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-10">
              <div className="mb-4 ">
                <img
                  src={emptyPageImg}
                  alt="No records"
                  className="w-full h-full"
                />
              </div>
              <div className="text-gray-500 text-lg font-medium">
                No records found.
              </div>
            </div>
          ) : (
            <MkdSimpleTable data={activities} columns={columns} />
          )}
          {pagination && pagination.total > 0 && (
            <PaginationBar
              currentPage={pagination.page}
              pageCount={pagination.num_pages}
              pageSize={pagination.limit}
              canPreviousPage={pagination.has_prev}
              canNextPage={pagination.has_next}
              updatePageSize={handleLimitChange}
              updateCurrentPage={handlePageChange}
              startSize={5}
              multiplier={5}
              canChangeLimit={true}
            />
          )}
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminRewardsAndReferralsListPage;
